# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Rust-based Solana blockchain data collector for meme token trading platforms. The system monitors Solana program events, processes bonding curve and staking events, and integrates with external services for user incentives and airdrops.

## Essential Commands

### Development
```bash
# Setup environment
cp .env.example .env

# Build project
cargo build

# Build for release
cargo build --release

# Run main event collector
cargo run --bin collect_curve_event

# Run price indexer
cargo run --bin index_sol_price

# Run staking event collector
cargo run --bin collect_stake_event

# Test airdrop trigger
cargo run --bin trigger_airdrop
```

### Using Just (task runner)
```bash
# Run curve event collector
just start-collect-curve-event

# Run price indexer
just start-price

# Run staking collector
just start-stake

# Test airdrop functionality
just test-bull
```

### Production Deployment
```bash
# Build release binaries
cargo build --release

# Use PM2 for process management (ecosystem.config.js configured)
pm2 start ecosystem.config.js
```

### Health Check
- HTTP health endpoint: `http://localhost:8888/api/v1/health`
- Returns "Hello, World!" when service is running

## Architecture Overview

### Core Components
- **Event Collectors** (`src/service/`): Monitor Solana blockchain events
  - `collect_curve_event`: Bonding curve event processing
  - `collect_stake_event`: Staking event monitoring
- **Data Layer** (`src/db/`): MongoDB repositories for persistent storage
  - Collections: coins, trades, holders, candles, token_prices, staking_events
- **Handlers** (`src/service/`): Business logic processors
  - `coin_handler`: Coin lifecycle management
  - `trade_handler`: Trading event processing
  - `holder_handler`: User holder tracking
- **External Integrations**: Redis caching, Jupiter API, external Node.js services

### Database Schema (MongoDB)
Key collections managed via repository pattern:
- `coins`: Token metadata and state
- `trades`: Trading transactions and events
- `holders`: Token holder information
- `candles`: Price candlestick data
- `token_prices`: Price tracking
- `staking_events`: Staking activity logs

### Configuration
- Environment-based config using `clap` with `.env` file support
- Key variables: `PROGRAM_ID`, `STAKE_PROGRAM_ID`, `MONGODB_URI`, `REDIS_HOST`
- Solana RPC endpoint configurable via `FULLNODE_URL`

### External Dependencies
- **Solana**: Primary blockchain interaction via `solana-client`, `solana-sdk`
- **Raydium**: AMM and CLMM integration for DEX operations
- **MongoDB**: Primary data persistence
- **Redis**: Caching and real-time data
- **Axum**: HTTP server framework for health checks

### Binary Targets
- `collect_curve_event`: Main event collector with HTTP health server
- `index_sol_price`: SOL price tracking service
- `collect_stake_event`: Staking event processor
- `create_pool`: Pool creation utility
- `trigger_airdrop`: Airdrop testing utility (mentioned in justfile as test-bull)

### Integration Patterns
- Event-driven architecture with Solana program monitoring
- Repository pattern for database operations
- Service layer abstraction for business logic
- External API communication for price feeds and integrations